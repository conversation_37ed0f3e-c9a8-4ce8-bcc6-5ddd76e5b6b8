import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { AdsService, Ad, Event } from './ads.service';
import { environment } from 'src/environments/environment';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzMessageModule, NzMessageService } from 'ng-zorro-antd/message';
import { EthiopianDatepickerComponent } from '../../../shared/components/ethiopian-datepicker/ethiopian-datepicker.component';

@Component({
  selector: 'app-ads',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzIconModule,
    NzUploadModule,
    NzCardModule,
    NzImageModule,
    NzSpinModule,
    NzSkeletonModule,
    NzMessageModule,
    EthiopianDatepickerComponent,
  ],
  templateUrl: './ads.component.html',
  styleUrls: ['./ads.component.scss'],
  providers: [AdsService],
})
export default class AdsComponent implements OnInit {
  ads: Ad[] = [];
  events: Event[] = [];
  combinedItems: (Ad | Event)[] = [];
  isModalVisible = false;
  modalTitle = '';
  adForm: FormGroup;
  currentAdId: number | null = null;
  currentEventId: number | null = null;
  fileList: NzUploadFile[] = [];
  public env = environment;
  currentPage = 1;
  pageSize = 9;
  totalItems = 0;
  expandedDescriptions: Set<number> = new Set();
  descriptionLimit = 100;
  isEvent = false; // Track whether user is creating an ad or event
  activeTab = 0; // 0 for ads, 1 for events, 2 for combined view
  loading: boolean = false;
  loadingStates = {
    fetching: false,
    creating: false,
    updating: false,
    deleting: false
  };

  constructor(
    private fb: FormBuilder,
    private modalService: NzModalService,
    private adsService: AdsService,
    private message: NzMessageService
  ) {
    this.adForm = this.fb.group({
      title: ['', [Validators.required]],
      description: ['', [Validators.required]],
      image: [null],
      expireAt: [null, [Validators.required]], // For ads: expiration date, For events: not used
      startDate: [null], // For events: start date
      endDate: [null], // For events: end date
      status: [1], // For events: status (1 = active, 0 = inactive)
      existingImage: [null], // For storing existing image URL during editing
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loadingStates.fetching = true;
    if (this.activeTab === 0) {
      this.loadAds();
    } else if (this.activeTab === 1) {
      this.loadEvents();
    } else {
      this.loadCombinedData();
    }
  }

  loadAds(): void {
    this.adsService.getAds(this.currentPage, this.pageSize).subscribe({
      next: (response: any) => {
        this.ads = response.data;
        this.totalItems = response.pagination?.totalItems || 0;
        if (this.activeTab === 2) {
          this.combinedItems = [...this.ads, ...this.events];
        }
        this.loadingStates.fetching = false;
      },
      error: (error) => {
        console.error('Error loading ads:', error);
        this.loadingStates.fetching = false;
      }
    });
  }

  loadEvents(): void {
    this.adsService.getEvents(this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.events = response.data;
        this.totalItems = response.pagination?.totalItems || 0;
        if (this.activeTab === 2) {
          this.combinedItems = [...this.ads, ...this.events];
        }
        this.loadingStates.fetching = false;
      },
      error: (error) => {
        console.error('Error loading events:', error);
        this.loadingStates.fetching = false;
      }
    });
  }

  loadCombinedData(): void {
    // Load both ads and events for combined view
    this.adsService.getAds(this.currentPage, this.pageSize).subscribe({
      next: (adsResponse: any) => {
        this.ads = adsResponse.data;
        this.adsService.getEvents(this.currentPage, this.pageSize).subscribe({
          next: (eventsResponse) => {
            this.events = eventsResponse.data;
            this.combinedItems = [...this.ads, ...this.events];
            this.totalItems = (adsResponse.pagination?.totalItems || 0) + (eventsResponse.pagination?.totalItems || 0);
            this.loadingStates.fetching = false;
          },
          error: (error) => {
            console.error('Error loading events for combined view:', error);
            this.loadingStates.fetching = false;
          }
        });
      },
      error: (error) => {
        console.error('Error loading ads for combined view:', error);
        this.loadingStates.fetching = false;
      }
    });
  }

  onTabChange(index: number): void {
    this.activeTab = index;
    this.currentPage = 1;
    this.loadData();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadData();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    this.loadData();
  }

  showModal(item?: Ad | Event, forceType?: 'ad' | 'event'): void {
    if (item) {
      // Determine if this is an ad or event based on the item properties
      this.isEvent = this.isEventItem(item);

      if (this.isEvent) {
        const event = item as Event;
        this.modalTitle = 'Edit Event';
        this.currentEventId = event.id;
        this.currentAdId = null;

        // Convert event data for form patching
        const formData: any = {
          title: event.title,
          description: event.desciption, // Note: backend uses 'desciption'
          status: event.status
        };

        // Handle existing image for events
        if (event.image_url) {
          // Set a flag to show existing image in the UI
          (formData as any).existingImage = event.image_url;
        }

        // Handle Ethiopian date conversion for events
        if (event.startDateEthiopian && Array.isArray(event.startDateEthiopian)) {
          const [year, month, day] = event.startDateEthiopian;
          formData.startDate = {
            ethiopian: { year, month, day }
          };
        }

        if (event.endDateEthiopian && Array.isArray(event.endDateEthiopian)) {
          const [year, month, day] = event.endDateEthiopian;
          formData.endDate = {
            ethiopian: { year, month, day }
          };
        }

        this.adForm.patchValue(formData);
      } else {
        const ad = item as Ad;
        this.modalTitle = 'Edit Ad';
        this.currentAdId = ad.id;
        this.currentEventId = null;

        // Convert the ad data for form patching
        const formData = { ...ad };

        // Handle Ethiopian date conversion if expiredAtEthiopian exists
        if (ad.expiredAtEthiopian && Array.isArray(ad.expiredAtEthiopian)) {
          const [year, month, day] = ad.expiredAtEthiopian;
          (formData as any).expireAt = {
            ethiopian: { year, month, day }
          };
        }

        // Handle existing image for ads
        if (ad.image) {
          (formData as any).existingImage = ad.image;
        }

        this.adForm.patchValue(formData);
      }
    } else {
      // Creating new item
      this.isEvent = forceType === 'event' || false;
      this.modalTitle = this.isEvent ? 'Create Event' : 'Create Ad';
      this.currentAdId = null;
      this.currentEventId = null;
      this.adForm.reset();
      this.adForm.patchValue({ status: 1 }); // Default status for events
      this.fileList = [];
    }

    this.isModalVisible = true;
    this.updateFormValidation(); // Set validation based on content type
  }

  private isEventItem(item: Ad | Event): boolean {
    // Check if item has event-specific properties
    return 'start_date' in item && 'end_date' in item && 'desciption' in item;
  }

  handleOk(): void {
    // Prevent multiple submissions
    if (this.loadingStates.creating || this.loadingStates.updating) {
      this.message.warning('Please wait for the current operation to complete.');
      return;
    }

    if (this.adForm.valid) {
      if (this.isEvent) {
        this.handleEventSubmit();
      } else {
        this.handleAdSubmit();
      }
    } else {
      this.message.error('Please fill in all required fields correctly.');
      Object.values(this.adForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  private handleAdSubmit(): void {
    const expireAt = this.adForm.get('expireAt')?.value;
    const formData = new FormData();
    formData.append('title', this.adForm.get('title')?.value);
    formData.append('description', this.adForm.get('description')?.value);
    formData.append('expireAt', expireAt.gregorian);

    if (this.fileList.length > 0) {
      formData.append('image', this.fileList[0] as any);
    }

    if (this.currentAdId) {
      this.loadingStates.updating = true;
      this.adsService.updateAd(this.currentAdId, formData).subscribe({
        next: () => {
          this.message.success('Advertisement updated successfully!');
          this.loadData();
          this.isModalVisible = false;
          this.fileList = [];
          this.loadingStates.updating = false;
        },
        error: (error) => {
          console.error('Error updating ad:', error);
          this.message.error('Failed to update advertisement. Please try again.');
          this.loadingStates.updating = false;
        }
      });
    } else {
      this.loadingStates.creating = true;
      this.adsService.createAd(formData).subscribe({
        next: () => {
          this.message.success('Advertisement created successfully!');
          this.loadData();
          this.isModalVisible = false;
          this.fileList = [];
          this.loadingStates.creating = false;
        },
        error: (error) => {
          console.error('Error creating ad:', error);
          this.message.error('Failed to create advertisement. Please try again.');
          this.loadingStates.creating = false;
        }
      });
    }
  }

  private handleEventSubmit(): void {
    const startDate = this.adForm.get('startDate')?.value;
    const endDate = this.adForm.get('endDate')?.value;

    const formData = new FormData();
    formData.append('title', this.adForm.get('title')?.value);
    formData.append('desciption', this.adForm.get('description')?.value); // Note: backend uses 'desciption'
    formData.append('start_date', startDate.gregorian);
    formData.append('end_date', endDate.gregorian);

    // Add image if uploaded
    if (this.fileList.length > 0) {
      formData.append('image', this.fileList[0] as any);
    }

    if (this.currentEventId) {
      this.loadingStates.updating = true;
      this.adsService.updateEvent(this.currentEventId, formData).subscribe({
        next: () => {
          this.message.success('Event updated successfully!');
          this.loadData();
          this.isModalVisible = false;
          this.fileList = [];
          this.loadingStates.updating = false;
        },
        error: (error) => {
          console.error('Error updating event:', error);
          this.message.error('Failed to update event. Please try again.');
          this.loadingStates.updating = false;
        }
      });
    } else {
      this.loadingStates.creating = true;
      this.adsService.createEvent(formData).subscribe({
        next: () => {
          this.message.success('Event created successfully!');
          this.loadData();
          this.isModalVisible = false;
          this.fileList = [];
          this.loadingStates.creating = false;
        },
        error: (error) => {
          console.error('Error creating event:', error);
          this.message.error('Failed to create event. Please try again.');
          this.loadingStates.creating = false;
        }
      });
    }
  }

  beforeUpload = (file: NzUploadFile): boolean => {
    // Validate file type
    const isImage = this.isValidImageFile(file);
    if (!isImage) {
      this.message.error('Please upload only image files (JPG, PNG, GIF, WebP)!');
      return false;
    }

    // Validate file size (max 5MB)
    const isLt5M = file.size! / 1024 / 1024 < 5;
    if (!isLt5M) {
      this.message.error('Image must be smaller than 5MB!');
      return false;
    }

    // Validate file name
    if (!file.name || file.name.trim() === '') {
      this.message.error('Invalid file name!');
      return false;
    }

    // Check for potentially dangerous file extensions
    const fileName = file.name.toLowerCase();
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    if (dangerousExtensions.some(ext => fileName.includes(ext))) {
      this.message.error('File type not allowed for security reasons!');
      return false;
    }

    // Only allow one file - replace existing
    if (this.fileList.length > 0) {
      this.message.info('Replacing existing image...');
    }

    // Set the file status to 'done' so it shows up in the list
    file.status = 'done';

    // Add the file to the list first
    this.fileList = [file];
    console.log('File added to list:', file.name, 'Status:', file.status, 'List length:', this.fileList.length);

    // Create preview URL for the image
    if (file.originFileObj) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        file.url = e.target.result;
        file['preview'] = e.target.result;
        // Update the file in the list to trigger change detection
        const updatedFile = { ...file };
        this.fileList = [updatedFile];
        console.log('Preview URL created for:', file.name);
      };
      reader.readAsDataURL(file.originFileObj);
    }

    // Perform async validation for image dimensions
    this.validateImageFile(file).then(isValid => {
      if (!isValid) {
        // Remove the file if validation fails
        this.fileList = this.fileList.filter(f => f.uid !== file.uid);
      }
    });

    return false;
  };

  private isValidImageFile(file: NzUploadFile): boolean {
    // Check file type by MIME type
    const validImageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];

    if (file.type && validImageTypes.includes(file.type)) {
      return true;
    }

    // Fallback: check file extension
    const fileName = file.name?.toLowerCase() || '';
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    return validExtensions.some(ext => fileName.endsWith(ext));
  }

  onRemoveFile = (file: any): boolean | Observable<boolean> => {
    // Handle both NzUploadFile and Event types from ng-zorro
    const uploadFile = file as NzUploadFile;
    this.fileList = this.fileList.filter(f => f.uid !== uploadFile.uid);
    return true;
  };

  onPreviewFile = (file: any): void => {
    // Handle both NzUploadFile and Event types
    const uploadFile = file as NzUploadFile;

    if (uploadFile.url || uploadFile['preview']) {
      // Open preview in a modal or new window
      const previewUrl = uploadFile.url || uploadFile['preview'];
      window.open(previewUrl, '_blank');
    } else if (uploadFile.originFileObj) {
      // Create preview URL for local file
      const reader = new FileReader();
      reader.onload = (e: any) => {
        window.open(e.target.result, '_blank');
      };
      reader.readAsDataURL(uploadFile.originFileObj);
    }
  };

  getFilePreviewUrl(file: NzUploadFile): string | undefined {
    if (file.url) {
      return file.url;
    }
    if (file['preview']) {
      return file['preview'];
    }
    if (file.originFileObj) {
      // Create object URL for preview
      return URL.createObjectURL(file.originFileObj);
    }
    return undefined;
  }

  validateImageDimensions(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);

        // Check minimum dimensions (optional)
        const minWidth = 100;
        const minHeight = 100;
        const maxWidth = 4000;
        const maxHeight = 4000;

        if (img.width < minWidth || img.height < minHeight) {
          this.message.error(`Image dimensions too small. Minimum size: ${minWidth}x${minHeight}px`);
          resolve(false);
          return;
        }

        if (img.width > maxWidth || img.height > maxHeight) {
          this.message.error(`Image dimensions too large. Maximum size: ${maxWidth}x${maxHeight}px`);
          resolve(false);
          return;
        }

        resolve(true);
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        this.message.error('Invalid image file or corrupted image');
        resolve(false);
      };

      img.src = url;
    });
  }

  async validateImageFile(file: NzUploadFile): Promise<boolean> {
    if (!file.originFileObj) {
      return false;
    }

    // Validate dimensions
    const dimensionsValid = await this.validateImageDimensions(file.originFileObj);
    if (!dimensionsValid) {
      return false;
    }

    // Additional validation can be added here
    return true;
  }

  getFileSize(file: NzUploadFile): string {
    if (!file.size) {
      return 'Unknown size';
    }

    const bytes = file.size;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes === 0) {
      return '0 Bytes';
    }

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = (bytes / Math.pow(1024, i)).toFixed(2);

    return `${size} ${sizes[i]}`;
  }

  onDrawerClose(): void {
    // This method is called when drawer close is attempted (ESC key, mask click, etc.)
    this.handleCancel();
  }

  handleCancel(): void {
    // Prevent closing during loading operations
    if (this.loadingStates.creating || this.loadingStates.updating) {
      this.message.warning('Please wait for the current operation to complete before closing.');
      return;
    }

    // Check if form has unsaved changes
    if (this.adForm.dirty && !this.isFormEmpty()) {
      this.modalService.confirm({
        nzTitle: 'Discard Changes?',
        nzContent: 'You have unsaved changes. Are you sure you want to close without saving?',
        nzOkText: 'Yes, Discard',
        nzOkType: 'primary',
        nzOkDanger: true,
        nzCancelText: 'Keep Editing',
        nzOnOk: () => {
          this.closeDrawer();
        }
      });
    } else {
      this.closeDrawer();
    }
  }

  private closeDrawer(): void {
    this.isModalVisible = false;
    this.fileList = [];
    this.adForm.reset();
    this.adForm.markAsPristine();
    this.adForm.markAsUntouched();
  }

  private isFormEmpty(): boolean {
    const formValue = this.adForm.value;
    return !formValue.title &&
           !formValue.description &&
           !formValue.expireAt &&
           !formValue.startDate &&
           !formValue.endDate &&
           this.fileList.length === 0;
  }

  onDateChange(date: Date): void {
    // Handle date change if needed
    console.log('Ethiopian date selected:', date);
  }

  onContentTypeChange(): void {
    // Update form validation based on content type
    this.updateFormValidation();

    // Reset form when switching between ad and event (only for new items)
    if (!this.currentAdId) {
      this.adForm.reset();
      this.fileList = [];
    }
  }

  private updateFormValidation(): void {
    const expireAtControl = this.adForm.get('expireAt');
    const startDateControl = this.adForm.get('startDate');
    const endDateControl = this.adForm.get('endDate');

    if (this.isEvent) {
      // For events: require start and end dates, make expireAt optional
      expireAtControl?.clearValidators();
      startDateControl?.setValidators([Validators.required]);
      endDateControl?.setValidators([Validators.required]);
    } else {
      // For ads: require expireAt, make start and end dates optional
      expireAtControl?.setValidators([Validators.required]);
      startDateControl?.clearValidators();
      endDateControl?.clearValidators();
    }

    // Update validity
    expireAtControl?.updateValueAndValidity();
    startDateControl?.updateValueAndValidity();
    endDateControl?.updateValueAndValidity();
  }

  formatEthiopianDate(dateInput: string | Date): string {
    try {
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
      // For now, return a formatted Gregorian date with Ethiopian context
      // In a full implementation, you would convert to Ethiopian calendar
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      return date.toLocaleDateString('en-US', options) + ' (Gregorian)';
    } catch (error) {
      return typeof dateInput === 'string' ? dateInput : dateInput.toString();
    }
  }

  deleteItem(item: Ad | Event): void {
    const isEvent = this.isEventItem(item);
    const itemType = isEvent ? 'event' : 'ad';
    const itemTitle = isEvent ? (item as Event).title : (item as Ad).title;

    this.modalService.confirm({
      nzTitle: `Are you sure you want to delete this ${itemType}?`,
      nzContent: `"${itemTitle}" will be permanently deleted. This action cannot be undone.`,
      nzOkText: 'Yes, Delete',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.loadingStates.deleting = true;
        if (isEvent) {
          this.adsService.deleteEvent(item.id).subscribe({
            next: () => {
              this.message.success('Event deleted successfully!');
              this.loadData();
              this.loadingStates.deleting = false;
            },
            error: (error) => {
              console.error('Error deleting event:', error);
              this.message.error('Failed to delete event. Please try again.');
              this.loadingStates.deleting = false;
            }
          });
        } else {
          this.adsService.deleteAd(item.id).subscribe({
            next: () => {
              this.message.success('Advertisement deleted successfully!');
              this.loadData();
              this.loadingStates.deleting = false;
            },
            error: (error) => {
              console.error('Error deleting ad:', error);
              this.message.error('Failed to delete advertisement. Please try again.');
              this.loadingStates.deleting = false;
            }
          });
        }
      },
      nzCancelText: 'Cancel',
    });
  }

  // Legacy method for backward compatibility
  deleteAd(adId: number): void {
    const ad = this.ads.find(a => a.id === adId);
    if (ad) {
      this.deleteItem(ad);
    }
  }

  getTruncatedDescription(description: string, itemId: number): string {
    if (!description) return '';

    if (this.expandedDescriptions.has(itemId) || description.length <= this.descriptionLimit) {
      return description;
    }

    return description.substring(0, this.descriptionLimit);
  }

  shouldShowReadMore(description: string, itemId: number): boolean {
    return description && description.length > this.descriptionLimit && !this.expandedDescriptions.has(itemId);
  }

  shouldShowReadLess(description: string, itemId: number): boolean {
    return description && description.length > this.descriptionLimit && this.expandedDescriptions.has(itemId);
  }

  toggleDescription(itemId: number): void {
    if (this.expandedDescriptions.has(itemId)) {
      this.expandedDescriptions.delete(itemId);
    } else {
      this.expandedDescriptions.add(itemId);
    }
  }

  getItemDescription(item: Ad | Event): string {
    return this.isEventItem(item) ? (item as Event).desciption : (item as Ad).description;
  }

  getItemTitle(item: Ad | Event): string {
    return item.title;
  }

  getItemType(item: Ad | Event): string {
    return this.isEventItem(item) ? 'event' : 'ad';
  }

  getItemStatus(item: Ad | Event): { isExpired: boolean; statusText: string; icon: string } {
    if (this.isEventItem(item)) {
      const event = item as Event;
      const now = new Date();
      const endDate = new Date(event.end_date);
      const isExpired = endDate < now || event.status === 0;

      return {
        isExpired,
        statusText: isExpired ? 'Ended' : 'Active',
        icon: isExpired ? 'icon-clock' : 'icon-check-circle'
      };
    } else {
      const ad = item as Ad;
      return {
        isExpired: ad.isExpired,
        statusText: ad.isExpired ? 'Expired' : 'Active',
        icon: ad.isExpired ? 'icon-clock' : 'icon-check-circle'
      };
    }
  }

  getItemDateInfo(item: Ad | Event): string {
    if (this.isEventItem(item)) {
      const event = item as Event;
      const startDate = this.formatEthiopianDate(event.start_date);
      const endDate = this.formatEthiopianDate(event.end_date);
      return `${startDate} - ${endDate}`;
    } else {
      const ad = item as Ad;
      return `Expires: ${this.formatEthiopianDate(ad.expireAt)}`;
    }
  }

  getCurrentItems(): (Ad | Event)[] {
    switch (this.activeTab) {
      case 0: return this.ads;
      case 1: return this.events;
      case 2: return this.combinedItems;
      default: return [];
    }
  }

  getItemImage(item: Ad | Event): string | undefined {
    return this.isEventItem(item) ? (item as Event).image_url : (item as Ad).image;
  }

  hasItemImage(item: Ad | Event): boolean {
    if (this.isEventItem(item)) {
      return !!(item as Event).image_url;
    } else {
      return !!(item as Ad).image;
    }
  }

  getExistingImageUrl(): string | undefined {
    const existingImage = this.adForm.get('existingImage')?.value;
    return existingImage;
  }

  getSubmitButtonText(): string {
    if (this.loadingStates.creating) {
      return this.isEvent ? 'Creating Event...' : 'Creating Advertisement...';
    }
    if (this.loadingStates.updating) {
      return this.isEvent ? 'Updating Event...' : 'Updating Advertisement...';
    }
    if (this.currentAdId || this.currentEventId) {
      return this.isEvent ? 'Update Event' : 'Update Advertisement';
    }
    return this.isEvent ? 'Create Event' : 'Create Advertisement';
  }

  getDrawerTitle(): string {
    if (this.loadingStates.creating) {
      return this.isEvent ? 'Creating Event...' : 'Creating Advertisement...';
    }
    if (this.loadingStates.updating) {
      return this.isEvent ? 'Updating Event...' : 'Updating Advertisement...';
    }
    if (this.currentAdId || this.currentEventId) {
      return this.isEvent ? 'Edit Event' : 'Edit Advertisement';
    }
    return this.isEvent ? 'Create New Event' : 'Create New Advertisement';
  }

  getDrawerSubtitle(): string {
    if (this.loadingStates.creating) {
      return this.isEvent ? 'Please wait while we create your event...' : 'Please wait while we create your advertisement...';
    }
    if (this.loadingStates.updating) {
      return this.isEvent ? 'Please wait while we update your event...' : 'Please wait while we update your advertisement...';
    }
    if (this.currentAdId || this.currentEventId) {
      return this.isEvent ? 'Update your event details and content' : 'Update your ad details and content';
    }
    return this.isEvent ? 'Fill in the details to create a new event' : 'Fill in the details to create a new ad';
  }
}
