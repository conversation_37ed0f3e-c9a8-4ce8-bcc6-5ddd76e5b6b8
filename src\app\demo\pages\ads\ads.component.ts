import { Component, OnInit } from '@angular/core';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { AdsService, Ad, Event } from './ads.service';
import { environment } from 'src/environments/environment';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzImageModule } from 'ng-zorro-antd/image';
import { EthiopianDatepickerComponent } from '../../../shared/components/ethiopian-datepicker/ethiopian-datepicker.component';

@Component({
  selector: 'app-ads',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzIconModule,
    NzUploadModule,
    NzCardModule,
    NzImageModule,
    EthiopianDatepickerComponent,
  ],
  templateUrl: './ads.component.html',
  styleUrls: ['./ads.component.scss'],
  providers: [AdsService],
})
export default class AdsComponent implements OnInit {
  ads: Ad[] = [];
  events: Event[] = [];
  combinedItems: (Ad | Event)[] = [];
  isModalVisible = false;
  modalTitle = '';
  adForm: FormGroup;
  currentAdId: number | null = null;
  currentEventId: number | null = null;
  fileList: NzUploadFile[] = [];
  public env = environment;
  currentPage = 1;
  pageSize = 1;
  totalItems = 0;
  expandedDescriptions: Set<number> = new Set();
  descriptionLimit = 100;
  isEvent = false; // Track whether user is creating an ad or event
  activeTab = 0; // 0 for ads, 1 for events, 2 for combined view

  constructor(
    private fb: FormBuilder,
    private modalService: NzModalService,
    private adsService: AdsService
  ) {
    this.adForm = this.fb.group({
      title: ['', [Validators.required]],
      description: ['', [Validators.required]],
      image: [null],
      expireAt: [null, [Validators.required]], // For ads: expiration date, For events: not used
      startDate: [null], // For events: start date
      endDate: [null], // For events: end date
      status: [1], // For events: status (1 = active, 0 = inactive)
      existingImage: [null], // For storing existing image URL during editing
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    if (this.activeTab === 0) {
      this.loadAds();
    } else if (this.activeTab === 1) {
      this.loadEvents();
    } else {
      this.loadCombinedData();
    }
  }

  loadAds(): void {
    this.adsService.getAds(this.currentPage, this.pageSize).subscribe((response: any) => {
      this.ads = response.data;
      this.totalItems = response.pagination?.totalItems || 0;
      if (this.activeTab === 2) {
        this.combinedItems = [...this.ads, ...this.events];
      }
    });
  }

  loadEvents(): void {
    this.adsService.getEvents(this.currentPage, this.pageSize).subscribe((response) => {
      this.events = response.data;
      this.totalItems = response.pagination?.totalItems || 0;
      if (this.activeTab === 2) {
        this.combinedItems = [...this.ads, ...this.events];
      }
    });
  }

  loadCombinedData(): void {
    // Load both ads and events for combined view
    this.adsService.getAds(this.currentPage, this.pageSize).subscribe((adsResponse: any) => {
      this.ads = adsResponse.data;
      this.adsService.getEvents(this.currentPage, this.pageSize).subscribe((eventsResponse) => {
        this.events = eventsResponse.data;
        this.combinedItems = [...this.ads, ...this.events];
        this.totalItems = (adsResponse.pagination?.totalItems || 0) + (eventsResponse.pagination?.totalItems || 0);
      });
    });
  }

  onTabChange(index: number): void {
    this.activeTab = index;
    this.currentPage = 1;
    this.loadData();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadData();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    this.loadData();
  }

  showModal(item?: Ad | Event, forceType?: 'ad' | 'event'): void {
    if (item) {
      // Determine if this is an ad or event based on the item properties
      this.isEvent = this.isEventItem(item);

      if (this.isEvent) {
        const event = item as Event;
        this.modalTitle = 'Edit Event';
        this.currentEventId = event.id;
        this.currentAdId = null;

        // Convert event data for form patching
        const formData: any = {
          title: event.title,
          description: event.desciption, // Note: backend uses 'desciption'
          status: event.status
        };

        // Handle existing image for events
        if (event.image_url) {
          // Set a flag to show existing image in the UI
          (formData as any).existingImage = event.image_url;
        }

        // Handle Ethiopian date conversion for events
        if (event.startDateEthiopian && Array.isArray(event.startDateEthiopian)) {
          const [year, month, day] = event.startDateEthiopian;
          formData.startDate = {
            ethiopian: { year, month, day }
          };
        }

        if (event.endDateEthiopian && Array.isArray(event.endDateEthiopian)) {
          const [year, month, day] = event.endDateEthiopian;
          formData.endDate = {
            ethiopian: { year, month, day }
          };
        }

        this.adForm.patchValue(formData);
      } else {
        const ad = item as Ad;
        this.modalTitle = 'Edit Ad';
        this.currentAdId = ad.id;
        this.currentEventId = null;

        // Convert the ad data for form patching
        const formData = { ...ad };

        // Handle Ethiopian date conversion if expiredAtEthiopian exists
        if (ad.expiredAtEthiopian && Array.isArray(ad.expiredAtEthiopian)) {
          const [year, month, day] = ad.expiredAtEthiopian;
          (formData as any).expireAt = {
            ethiopian: { year, month, day }
          };
        }

        // Handle existing image for ads
        if (ad.image) {
          (formData as any).existingImage = ad.image;
        }

        this.adForm.patchValue(formData);
      }
    } else {
      // Creating new item
      this.isEvent = forceType === 'event' || false;
      this.modalTitle = this.isEvent ? 'Create Event' : 'Create Ad';
      this.currentAdId = null;
      this.currentEventId = null;
      this.adForm.reset();
      this.adForm.patchValue({ status: 1 }); // Default status for events
      this.fileList = [];
    }

    this.isModalVisible = true;
    this.updateFormValidation(); // Set validation based on content type
  }

  private isEventItem(item: Ad | Event): boolean {
    // Check if item has event-specific properties
    return 'start_date' in item && 'end_date' in item && 'desciption' in item;
  }

  handleOk(): void {
    if (this.adForm.valid) {
      if (this.isEvent) {
        this.handleEventSubmit();
      } else {
        this.handleAdSubmit();
      }
    } else {
      Object.values(this.adForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  private handleAdSubmit(): void {
    const expireAt = this.adForm.get('expireAt')?.value;
    const formData = new FormData();
    formData.append('title', this.adForm.get('title')?.value);
    formData.append('description', this.adForm.get('description')?.value);
    formData.append('expireAt', expireAt.gregorian);

    if (this.fileList.length > 0) {
      formData.append('image', this.fileList[0] as any);
    }

    if (this.currentAdId) {
      this.adsService.updateAd(this.currentAdId, formData).subscribe(() => {
        this.loadData();
        this.isModalVisible = false;
        this.fileList = [];
      });
    } else {
      this.adsService.createAd(formData).subscribe(() => {
        this.loadData();
        this.isModalVisible = false;
        this.fileList = [];
      });
    }
  }

  private handleEventSubmit(): void {
    const startDate = this.adForm.get('startDate')?.value;
    const endDate = this.adForm.get('endDate')?.value;

    const formData = new FormData();
    formData.append('title', this.adForm.get('title')?.value);
    formData.append('desciption', this.adForm.get('description')?.value); // Note: backend uses 'desciption'
    formData.append('start_date', startDate.gregorian);
    formData.append('end_date', endDate.gregorian);


    // Add image if uploaded
    if (this.fileList.length > 0) {
      formData.append('image', this.fileList[0] as any);
    }

    if (this.currentEventId) {
      this.adsService.updateEvent(this.currentEventId, formData).subscribe(() => {
        this.loadData();
        this.isModalVisible = false;
        this.fileList = [];
      });
    } else {
      this.adsService.createEvent(formData).subscribe(() => {
        this.loadData();
        this.isModalVisible = false;
        this.fileList = [];
      });
    }
  }

  beforeUpload = (file: NzUploadFile): boolean => {
    this.fileList = [file];
    return false;
  };

  handleCancel(): void {
    this.isModalVisible = false;
    this.fileList = [];
  }

  onDateChange(date: Date): void {
    // Handle date change if needed
    console.log('Ethiopian date selected:', date);
  }

  onContentTypeChange(): void {
    // Update form validation based on content type
    this.updateFormValidation();

    // Reset form when switching between ad and event (only for new items)
    if (!this.currentAdId) {
      this.adForm.reset();
      this.fileList = [];
    }
  }

  private updateFormValidation(): void {
    const expireAtControl = this.adForm.get('expireAt');
    const startDateControl = this.adForm.get('startDate');
    const endDateControl = this.adForm.get('endDate');

    if (this.isEvent) {
      // For events: require start and end dates, make expireAt optional
      expireAtControl?.clearValidators();
      startDateControl?.setValidators([Validators.required]);
      endDateControl?.setValidators([Validators.required]);
    } else {
      // For ads: require expireAt, make start and end dates optional
      expireAtControl?.setValidators([Validators.required]);
      startDateControl?.clearValidators();
      endDateControl?.clearValidators();
    }

    // Update validity
    expireAtControl?.updateValueAndValidity();
    startDateControl?.updateValueAndValidity();
    endDateControl?.updateValueAndValidity();
  }

  formatEthiopianDate(dateInput: string | Date): string {
    try {
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
      // For now, return a formatted Gregorian date with Ethiopian context
      // In a full implementation, you would convert to Ethiopian calendar
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      return date.toLocaleDateString('en-US', options) + ' (Gregorian)';
    } catch (error) {
      return typeof dateInput === 'string' ? dateInput : dateInput.toString();
    }
  }

  deleteItem(item: Ad | Event): void {
    const isEvent = this.isEventItem(item);
    const itemType = isEvent ? 'event' : 'ad';
    const itemTitle = isEvent ? (item as Event).title : (item as Ad).title;

    this.modalService.confirm({
      nzTitle: `Are you sure you want to delete this ${itemType}?`,
      nzContent: `"${itemTitle}" will be permanently deleted. This action cannot be undone.`,
      nzOkText: 'Yes, Delete',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        if (isEvent) {
          this.adsService.deleteEvent(item.id).subscribe(() => {
            this.loadData();
          });
        } else {
          this.adsService.deleteAd(item.id).subscribe(() => {
            this.loadData();
          });
        }
      },
      nzCancelText: 'Cancel',
    });
  }

  // Legacy method for backward compatibility
  deleteAd(adId: number): void {
    const ad = this.ads.find(a => a.id === adId);
    if (ad) {
      this.deleteItem(ad);
    }
  }

  getTruncatedDescription(description: string, itemId: number): string {
    if (!description) return '';

    if (this.expandedDescriptions.has(itemId) || description.length <= this.descriptionLimit) {
      return description;
    }

    return description.substring(0, this.descriptionLimit);
  }

  shouldShowReadMore(description: string, itemId: number): boolean {
    return description && description.length > this.descriptionLimit && !this.expandedDescriptions.has(itemId);
  }

  shouldShowReadLess(description: string, itemId: number): boolean {
    return description && description.length > this.descriptionLimit && this.expandedDescriptions.has(itemId);
  }

  toggleDescription(itemId: number): void {
    if (this.expandedDescriptions.has(itemId)) {
      this.expandedDescriptions.delete(itemId);
    } else {
      this.expandedDescriptions.add(itemId);
    }
  }

  getItemDescription(item: Ad | Event): string {
    return this.isEventItem(item) ? (item as Event).desciption : (item as Ad).description;
  }

  getItemTitle(item: Ad | Event): string {
    return item.title;
  }

  getItemType(item: Ad | Event): string {
    return this.isEventItem(item) ? 'event' : 'ad';
  }

  getItemStatus(item: Ad | Event): { isExpired: boolean; statusText: string; icon: string } {
    if (this.isEventItem(item)) {
      const event = item as Event;
      const now = new Date();
      const endDate = new Date(event.end_date);
      const isExpired = endDate < now || event.status === 0;

      return {
        isExpired,
        statusText: isExpired ? 'Ended' : 'Active',
        icon: isExpired ? 'icon-clock' : 'icon-check-circle'
      };
    } else {
      const ad = item as Ad;
      return {
        isExpired: ad.isExpired,
        statusText: ad.isExpired ? 'Expired' : 'Active',
        icon: ad.isExpired ? 'icon-clock' : 'icon-check-circle'
      };
    }
  }

  getItemDateInfo(item: Ad | Event): string {
    if (this.isEventItem(item)) {
      const event = item as Event;
      const startDate = this.formatEthiopianDate(event.start_date);
      const endDate = this.formatEthiopianDate(event.end_date);
      return `${startDate} - ${endDate}`;
    } else {
      const ad = item as Ad;
      return `Expires: ${this.formatEthiopianDate(ad.expireAt)}`;
    }
  }

  getCurrentItems(): (Ad | Event)[] {
    switch (this.activeTab) {
      case 0: return this.ads;
      case 1: return this.events;
      case 2: return this.combinedItems;
      default: return [];
    }
  }

  getItemImage(item: Ad | Event): string | undefined {
    return this.isEventItem(item) ? (item as Event).image_url : (item as Ad).image;
  }

  hasItemImage(item: Ad | Event): boolean {
    if (this.isEventItem(item)) {
      return !!(item as Event).image_url;
    } else {
      return !!(item as Ad).image;
    }
  }

  getExistingImageUrl(): string | undefined {
    const existingImage = this.adForm.get('existingImage')?.value;
    return existingImage;
  }
}
